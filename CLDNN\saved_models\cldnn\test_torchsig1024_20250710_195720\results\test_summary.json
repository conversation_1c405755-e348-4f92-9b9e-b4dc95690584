{"overall_metrics": {"accuracy": 58.315384615384616, "macro_f1": 57.43211091201804, "kappa": 0.5657852564102563}, "model_complexity": {"macs": "1.123G", "parameters": "950.489K", "macs_raw": 1122599296.0, "params_raw": 950489.0}, "inference_performance": {"avg_inference_time_ms": 0.11738973970596607, "std_inference_time_ms": 0.01394226322829598, "min_inference_time_ms": 0.1108311116695404, "max_inference_time_ms": 0.4712343215942383}, "dataset_info": {"total_samples": 208000, "dataset_type": "torchsig1024", "input_shape": [2, 1024], "num_classes": 25, "snr_range": [0.0, 30.0]}, "test_info": {"model_path": "./saved_models/cldnn/torchsig1024_20250709_104433/models/best_model.pth", "config_path": "config.yaml", "test_date": "2025-07-10 19:58:50"}}