{"overall_metrics": {"accuracy": 61.916449739948774, "macro_f1": 63.835498924914766, "kappa": 0.602606367613851}, "model_complexity": {"macs": "100.576M", "parameters": "385.290K", "macs_raw": 100576416.0, "params_raw": 385290.0}, "inference_performance": {"avg_inference_time_ms": 0.062219077072067544, "std_inference_time_ms": 0.03659574476711147, "min_inference_time_ms": 0.036228448152542114, "max_inference_time_ms": 1.5138275921344757}, "dataset_info": {"total_samples": 383386, "dataset_type": "rml201801a", "input_shape": [2, 1024], "num_classes": 24, "snr_range": [-20.0, 30.0]}, "test_info": {"model_path": "./saved_models/awn/rml201801a_20250620_113626/models/best_model.pth", "config_path": "config.yaml", "test_date": "2025-06-20 23:12:22"}}