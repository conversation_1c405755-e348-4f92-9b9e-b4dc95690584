dataset_info:
  dataset_type: torchsig2048
  input_shape: !!python/tuple
  - 2
  - 2048
  num_classes: 25
  snr_range:
  - 0.0
  - 30.0
  total_samples: 208000
inference_performance:
  avg_inference_time_ms: 0.07105368948899782
  max_inference_time_ms: 0.904567539691925
  min_inference_time_ms: 0.032957643270492554
  std_inference_time_ms: 0.027467495586567484
model_complexity:
  macs: 8.230M
  macs_raw: 8229504.0
  parameters: 824.857K
  params_raw: 824857.0
overall_metrics:
  accuracy: 50.69326923076923
  kappa: 0.48638822115384617
  macro_f1: 49.81023135242495
test_info:
  config_path: config.yaml
  model_path: saved_models/mamc/torchsig2048_20250709_164326/models/best_model.pth
  test_date: '2025-07-09 21:55:51'
