dataset_info:
  dataset_type: torchsig2048
  input_shape: !!python/tuple
  - 2
  - 2048
  num_classes: 25
  snr_range:
  - 0.0
  - 30.0
  total_samples: 208000
inference_performance:
  avg_inference_time_ms: 0.1084960183271995
  max_inference_time_ms: 0.5168914794921875
  min_inference_time_ms: 0.08394569158554077
  std_inference_time_ms: 0.02741276023580905
model_complexity:
  macs: 545.001M
  macs_raw: 545001088.0
  parameters: 353.241K
  params_raw: 353241.0
overall_metrics:
  accuracy: 66.83076923076923
  kappa: 0.6544871794871795
  macro_f1: 65.74441009530379
test_info:
  config_path: config.yaml
  model_path: ./saved_models/wnn_mrnn/torchsig2048_20250710_053409/models/best_model.pth
  test_date: '2025-07-11 15:49:20'
