import os
import json
import pandas as pd
from pathlib import Path
import glob

# ==================== 配置区域 ====================
# 在这里自定义要处理的模型文件夹和数据集类型
#
# 使用说明：
# 1. 手动配置：修改下面的 MODEL_FOLDERS 和 DATASETS 列表
# 2. 自动检测：将 AUTO_DETECT_MODELS 设为 True，脚本会自动扫描当前目录下包含 saved_models 的文件夹
# 3. Excel表格中的模型顺序将按照 MODEL_FOLDERS 列表的顺序显示

# 定义要处理的模型文件夹（根据实际存在的文件夹修改）
MODEL_FOLDERS = [
    'CLDNN',
    'AWN',
    'MAMC',
    'MAWDN',
    'WNN-MRNN'
]
# 定义要处理的数据集类型（根据实际训练的数据集修改）
#DATASETS = ['rml201801a', 'rml', 'hisar', 'torchsig1024', 'torchsig2048', 'torchsig4096']
DATASETS = ['torchsig1024']
# 定义要处理的数据集类型（根据实际训练的数据集修改）
# DATASETS = [ 'rml']

# 是否自动检测模型文件夹（设为True时会自动扫描当前目录下的所有文件夹）
AUTO_DETECT_MODELS = False

# ==================== 配置区域结束 ====================

def auto_detect_model_folders():
    """自动检测当前目录下包含saved_models子目录的文件夹"""
    detected_folders = []
    current_dir = os.getcwd()

    for item in os.listdir(current_dir):
        item_path = os.path.join(current_dir, item)
        if os.path.isdir(item_path):
            saved_models_path = os.path.join(item_path, 'saved_models')
            if os.path.exists(saved_models_path):
                detected_folders.append(item)

    return sorted(detected_folders)

def find_test_summary_files():
    """扫描所有模型文件夹，查找test_summary.json文件"""

    # 确定要使用的模型文件夹列表
    if AUTO_DETECT_MODELS:
        model_folders = auto_detect_model_folders()
        print(f"自动检测到的模型文件夹: {model_folders}")
    else:
        model_folders = MODEL_FOLDERS
        print(f"使用配置的模型文件夹: {model_folders}")

    # 存储找到的文件信息
    results = {}

    for dataset in DATASETS:
        results[dataset] = {}

        for model_folder in model_folders:
            results[dataset][model_folder] = None
            
            # 查找saved_models目录
            saved_models_path = os.path.join(model_folder, 'saved_models')
            if not os.path.exists(saved_models_path):
                print(f"警告: {model_folder} 中未找到 saved_models 目录")
                continue
            
            # 查找模型特定的子目录
            model_subdir = None
            for item in os.listdir(saved_models_path):
                item_path = os.path.join(saved_models_path, item)
                if os.path.isdir(item_path):
                    model_subdir = item_path
                    break
            
            if not model_subdir:
                print(f"警告: {model_folder}/saved_models 中未找到模型子目录")
                continue
            
            # 查找数据集特定的测试目录
            test_dirs = []
            for item in os.listdir(model_subdir):
                if item.startswith(f"test_{dataset}_"):
                    test_dirs.append(item)
            
            if not test_dirs:
                print(f"信息: {model_folder} 中未找到 {dataset} 数据集的测试结果")
                continue
            
            # 选择最新的测试目录（按时间戳排序）
            test_dirs.sort(reverse=True)
            latest_dir = test_dirs[0]
            
            # 查找test_summary.json文件
            summary_file = os.path.join(model_subdir, latest_dir, 'results', 'test_summary.json')
            
            if os.path.exists(summary_file):
                results[dataset][model_folder] = summary_file
                print(f"找到: {model_folder} - {dataset} - {summary_file}")
            else:
                print(f"警告: 未找到 {summary_file}")
    
    return results

def extract_data_from_test_json(json_file_path):
    """从test_summary.json文件中提取数据"""
    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 提取需要的字段
        extracted_data = {
            'accuracy': data.get('overall_metrics', {}).get('accuracy', ''),
            'macro_f1': data.get('overall_metrics', {}).get('macro_f1', ''),
            'kappa': data.get('overall_metrics', {}).get('kappa', ''),
            'macs': data.get('model_complexity', {}).get('macs', ''),
            'parameters': data.get('model_complexity', {}).get('parameters', ''),
            'macs_raw': data.get('model_complexity', {}).get('macs_raw', ''),
            'params_raw': data.get('model_complexity', {}).get('params_raw', ''),
            'avg_inference_time_ms': data.get('inference_performance', {}).get('avg_inference_time_ms', ''),
            'std_inference_time_ms': data.get('inference_performance', {}).get('std_inference_time_ms', ''),
            'min_inference_time_ms': data.get('inference_performance', {}).get('min_inference_time_ms', ''),
            'max_inference_time_ms': data.get('inference_performance', {}).get('max_inference_time_ms', '')
        }
        
        return extracted_data
        
    except Exception as e:
        print(f"错误: 无法读取 {json_file_path}: {e}")
        return None

def create_excel_tables(results_data):
    """为每个数据集创建Excel表格"""

    # 确定要使用的模型文件夹列表（与find_test_summary_files中的逻辑保持一致）
    if AUTO_DETECT_MODELS:
        model_folders = auto_detect_model_folders()
    else:
        model_folders = MODEL_FOLDERS

    # 定义列名（中文）
    columns = [
        '模型名称',
        '测试准确率',
        'Macro-F1',
        'Kappa',
        'MACs',
        '参数量',
        'MACs(原始值)',
        '参数量(原始值)',
        '平均推理时间(ms)',
        '推理时间标准差(ms)',
        '最小推理时间(ms)',
        '最大推理时间(ms)'
    ]

    for dataset in results_data.keys():
        print(f"\n创建 {dataset} 数据集的Excel表格...")

        # 创建DataFrame
        rows = []

        for model in model_folders:
            if model in results_data[dataset] and results_data[dataset][model]:
                # 提取数据
                json_file = results_data[dataset][model]
                data = extract_data_from_test_json(json_file)
                
                if data:
                    row = [
                        model,
                        data['accuracy'],
                        data['macro_f1'],
                        data['kappa'],
                        data['macs'],
                        data['parameters'],
                        data['macs_raw'],
                        data['params_raw'],
                        data['avg_inference_time_ms'],
                        data['std_inference_time_ms'],
                        data['min_inference_time_ms'],
                        data['max_inference_time_ms']
                    ]
                else:
                    # 数据提取失败，创建空行
                    row = [model] + [''] * 11
            else:
                # 没有找到对应的结果文件，创建空行
                row = [model] + [''] * 11
            
            rows.append(row)
        
        # 创建DataFrame
        df = pd.DataFrame(rows, columns=columns)
        
        # 保存为Excel文件
        excel_filename = f"test_{dataset}.xlsx"
        df.to_excel(excel_filename, index=False, engine='openpyxl')
        print(f"已保存: {excel_filename}")

def main():
    """主函数"""
    print("开始扫描测试结果文件...")
    
    # 查找所有test_summary.json文件
    results_data = find_test_summary_files()
    
    print(f"\n扫描完成，开始创建Excel表格...")
    
    # 创建Excel表格
    create_excel_tables(results_data)
    
    print(f"\n所有Excel表格创建完成！")

if __name__ == '__main__':
    main()
